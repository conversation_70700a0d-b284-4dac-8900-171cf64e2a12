'use client';

import React from 'react';
import { useAuth, useRoleAccess } from '@/lib/auth/context';
import { UserRole } from '@/types';

interface RoleGuardProps {
  children: React.ReactNode;
  allowedRoles: UserRole[];
  fallback?: React.ReactNode;
  requireAuth?: boolean;
}

export function RoleGuard({ 
  children, 
  allowedRoles, 
  fallback,
  requireAuth = true 
}: RoleGuardProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const { hasAnyRole } = useRoleAccess();

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Check authentication if required
  if (requireAuth && !isAuthenticated) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-secondary-900 mb-4">Authentication Required</h1>
          <p className="text-secondary-600 mb-6">You need to be logged in to access this page.</p>
          <a 
            href="/login" 
            className="btn-primary"
          >
            Go to Login
          </a>
        </div>
      </div>
    );
  }

  // Check role permissions
  if (requireAuth && !hasAnyRole(allowedRoles)) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-secondary-900 mb-4">Access Denied</h1>
          <p className="text-secondary-600 mb-6">
            You don't have permission to access this page.
          </p>
          <a 
            href="/dashboard" 
            className="btn-primary"
          >
            Go to Dashboard
          </a>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// Specific role guard components for convenience
export function StudentGuard({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={[UserRole.STUDENT]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function InstructorGuard({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={[UserRole.INSTRUCTOR, UserRole.ADMIN]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function ProctorGuard({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={[UserRole.PROCTOR, UserRole.ADMIN]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function AdminGuard({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={[UserRole.ADMIN]} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

// Component to conditionally render based on roles
interface ConditionalRenderProps {
  children: React.ReactNode;
  allowedRoles: UserRole[];
  requireAuth?: boolean;
}

export function ConditionalRender({ 
  children, 
  allowedRoles, 
  requireAuth = true 
}: ConditionalRenderProps) {
  const { isAuthenticated } = useAuth();
  const { hasAnyRole } = useRoleAccess();

  if (requireAuth && !isAuthenticated) {
    return null;
  }

  if (requireAuth && !hasAnyRole(allowedRoles)) {
    return null;
  }

  return <>{children}</>;
}
