@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f1f5f9;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), system-ui, sans-serif;
  line-height: 1.6;
}

@layer components {
  /* Button Variants */
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-secondary-100 hover:bg-secondary-200 text-secondary-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-outline {
    @apply border border-primary-600 text-primary-600 hover:bg-primary-50 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  /* Form Elements */
  .form-input {
    @apply w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .form-label {
    @apply block text-sm font-medium text-secondary-700 mb-1;
  }

  .form-error {
    @apply text-sm text-error-500 mt-1;
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-secondary-200 p-6;
  }

  .card-header {
    @apply border-b border-secondary-200 pb-4 mb-4;
  }

  /* Navigation */
  .nav-link {
    @apply text-secondary-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
  }

  .nav-link-active {
    @apply text-primary-600 bg-primary-50 px-3 py-2 rounded-md text-sm font-medium;
  }

  /* Status Indicators */
  .status-success {
    @apply bg-green-50 text-green-800 border border-green-200 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-warning {
    @apply bg-yellow-50 text-yellow-800 border border-yellow-200 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-error {
    @apply bg-red-50 text-red-800 border border-red-200 px-2 py-1 rounded-full text-xs font-medium;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .shadow-strong {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 4px 25px -5px rgba(0, 0, 0, 0.1);
  }
}
