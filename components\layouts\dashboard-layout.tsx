'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  Bars3Icon, 
  XMarkIcon,
  HomeIcon,
  DocumentTextIcon,
  UserGroupIcon,
  CogIcon,
  BellIcon,
  UserCircleIcon,
} from '@heroicons/react/24/outline';
import { useAuth, useRoleAccess } from '@/lib/auth/context';
import { UserRole } from '@/types';
import { cn } from '@/lib/utils';

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  roles: UserRole[];
}

const navigation: NavItem[] = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon, roles: [UserRole.STUDENT, UserRole.INSTRUCTOR, UserRole.PROCTOR, UserRole.ADMIN] },
  { name: 'Exams', href: '/student/dashboard', icon: DocumentTextIcon, roles: [UserRole.STUDENT] },
  { name: 'My Exams', href: '/instructor/dashboard', icon: DocumentTextIcon, roles: [UserRole.INSTRUCTOR] },
  { name: 'Questions', href: '/instructor/questions', icon: DocumentTextIcon, roles: [UserRole.INSTRUCTOR] },
  { name: 'Monitoring', href: '/proctor/dashboard', icon: DocumentTextIcon, roles: [UserRole.PROCTOR] },
  { name: 'Users', href: '/admin/users', icon: UserGroupIcon, roles: [UserRole.ADMIN] },
  { name: 'Settings', href: '/admin/settings', icon: CogIcon, roles: [UserRole.ADMIN] },
];

interface DashboardLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
}

export function DashboardLayout({ children, title, description }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const pathname = usePathname();
  const { user, logout } = useAuth();
  const { hasAnyRole } = useRoleAccess();

  const filteredNavigation = navigation.filter(item => 
    hasAnyRole(item.roles)
  );

  const handleLogout = async () => {
    await logout();
    window.location.href = '/login';
  };

  return (
    <div className="min-h-screen bg-secondary-50">
      {/* Mobile sidebar */}
      <div className={cn(
        'fixed inset-0 z-50 lg:hidden',
        sidebarOpen ? 'block' : 'hidden'
      )}>
        <div className="fixed inset-0 bg-secondary-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-white">
          <div className="flex h-16 items-center justify-between px-4">
            <h1 className="text-xl font-bold text-primary-700">Exam Portal</h1>
            <button
              type="button"
              className="text-secondary-400 hover:text-secondary-500"
              onClick={() => setSidebarOpen(false)}
              aria-label="Close sidebar"
              title="Close sidebar"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {filteredNavigation.map((item) => {
              const isActive = pathname.startsWith(item.href);
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    'group flex items-center px-2 py-2 text-sm font-medium rounded-md',
                    isActive
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'
                  )}
                  onClick={() => setSidebarOpen(false)}
                >
                  <item.icon
                    className={cn(
                      'mr-3 h-5 w-5 flex-shrink-0',
                      isActive ? 'text-primary-500' : 'text-secondary-400 group-hover:text-secondary-500'
                    )}
                  />
                  {item.name}
                </Link>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white border-r border-secondary-200">
          <div className="flex h-16 items-center px-4">
            <h1 className="text-xl font-bold text-primary-700">Exam Portal</h1>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {filteredNavigation.map((item) => {
              const isActive = pathname.startsWith(item.href);
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    'group flex items-center px-2 py-2 text-sm font-medium rounded-md',
                    isActive
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'
                  )}
                >
                  <item.icon
                    className={cn(
                      'mr-3 h-5 w-5 flex-shrink-0',
                      isActive ? 'text-primary-500' : 'text-secondary-400 group-hover:text-secondary-500'
                    )}
                  />
                  {item.name}
                </Link>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top bar */}
        <div className="sticky top-0 z-40 bg-white shadow-sm border-b border-secondary-200">
          <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
            <button
              type="button"
              className="text-secondary-500 hover:text-secondary-600 lg:hidden"
              onClick={() => setSidebarOpen(true)}
              aria-label="Open sidebar"
              title="Open sidebar"
            >
              <Bars3Icon className="h-6 w-6" />
            </button>

            <div className="flex-1 lg:flex lg:items-center lg:justify-between">
              <div>
                {title && (
                  <h1 className="text-2xl font-bold text-secondary-900">{title}</h1>
                )}
                {description && (
                  <p className="mt-1 text-sm text-secondary-600">{description}</p>
                )}
              </div>

              <div className="flex items-center space-x-4">
                <button
                  type="button"
                  className="text-secondary-400 hover:text-secondary-500"
                  title="Notifications"
                  aria-label="Notifications"
                >
                  <BellIcon className="h-6 w-6" />
                </button>

                <div className="relative">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <UserCircleIcon className="h-8 w-8 text-secondary-400" />
                    </div>
                    <div className="hidden md:block">
                      <div className="text-sm font-medium text-secondary-900">
                        {user?.firstName} {user?.lastName}
                      </div>
                      <div className="text-xs text-secondary-500 capitalize">
                        {user?.role.toLowerCase()}
                      </div>
                    </div>
                    <button
                      onClick={handleLogout}
                      className="text-sm text-secondary-600 hover:text-secondary-900"
                    >
                      Logout
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
