import { z } from 'zod';
import { UserRole, QuestionType, Difficulty, ExamStatus } from '@/types';

// Base validation schemas
export const emailSchema = z
  .string()
  .email('Please enter a valid email address')
  .min(1, 'Email is required');

export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 
    'Password must contain at least one uppercase letter, one lowercase letter, and one number');

export const phoneSchema = z
  .string()
  .regex(/^\+?1?[-.\s]?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/, 
    'Please enter a valid phone number')
  .optional();

export const nameSchema = z
  .string()
  .min(2, 'Name must be at least 2 characters')
  .max(50, 'Name must be less than 50 characters')
  .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes');

// Authentication schemas
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
});

export const signupSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string(),
  firstName: nameSchema,
  lastName: nameSchema,
  role: z.nativeEnum(UserRole),
  phoneNumber: phoneSchema,
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const forgotPasswordSchema = z.object({
  email: emailSchema,
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const otpSchema = z.object({
  otp: z.string().length(6, 'OTP must be 6 digits').regex(/^\d+$/, 'OTP must contain only numbers'),
});

// Profile schemas
export const profileSetupSchema = z.object({
  firstName: nameSchema,
  lastName: nameSchema,
  phoneNumber: phoneSchema,
  photoUrl: z.string().url('Please enter a valid URL').optional(),
  idPhotoUrl: z.string().url('Please enter a valid URL').optional(),
});

export const updateProfileSchema = z.object({
  firstName: nameSchema.optional(),
  lastName: nameSchema.optional(),
  phoneNumber: phoneSchema,
  photoUrl: z.string().url('Please enter a valid URL').optional(),
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Question schemas
export const questionOptionSchema = z.object({
  text: z.string().min(1, 'Option text is required').max(500, 'Option text is too long'),
  isCorrect: z.boolean(),
  order: z.number().int().min(0),
});

export const questionSchema = z.object({
  title: z.string().min(1, 'Question title is required').max(200, 'Title is too long'),
  content: z.string().min(1, 'Question content is required').max(5000, 'Content is too long'),
  type: z.nativeEnum(QuestionType),
  marks: z.number().int().min(1, 'Marks must be at least 1').max(100, 'Marks cannot exceed 100'),
  options: z.array(questionOptionSchema).optional(),
  correctAnswer: z.string().max(1000, 'Answer is too long').optional(),
  difficulty: z.nativeEnum(Difficulty),
  category: z.string().max(100, 'Category name is too long').optional(),
  tags: z.array(z.string().max(50, 'Tag is too long')).max(10, 'Too many tags'),
}).refine((data) => {
  // Validate that MCQ and multiple select questions have options
  if (data.type === QuestionType.MULTIPLE_CHOICE || data.type === QuestionType.MULTIPLE_SELECT) {
    return data.options && data.options.length >= 2;
  }
  return true;
}, {
  message: "Multiple choice questions must have at least 2 options",
  path: ["options"],
}).refine((data) => {
  // Validate that MCQ has exactly one correct answer
  if (data.type === QuestionType.MULTIPLE_CHOICE && data.options) {
    const correctCount = data.options.filter(opt => opt.isCorrect).length;
    return correctCount === 1;
  }
  return true;
}, {
  message: "Multiple choice questions must have exactly one correct answer",
  path: ["options"],
}).refine((data) => {
  // Validate that multiple select has at least one correct answer
  if (data.type === QuestionType.MULTIPLE_SELECT && data.options) {
    const correctCount = data.options.filter(opt => opt.isCorrect).length;
    return correctCount >= 1;
  }
  return true;
}, {
  message: "Multiple select questions must have at least one correct answer",
  path: ["options"],
});

// Exam schemas
const examBaseSchema = z.object({
  title: z.string().min(1, 'Exam title is required').max(200, 'Title is too long'),
  description: z.string().max(1000, 'Description is too long').optional(),
  duration: z.number().int().min(1, 'Duration must be at least 1 minute').max(480, 'Duration cannot exceed 8 hours'),
  totalMarks: z.number().int().min(1, 'Total marks must be at least 1'),
  passingMarks: z.number().int().min(0).optional(),
  randomizeQuestions: z.boolean(),
  allowReview: z.boolean(),
  maxAttempts: z.number().int().min(1, 'Must allow at least 1 attempt').max(10, 'Cannot exceed 10 attempts'),
  requireProctoring: z.boolean(),
  requireIdCheck: z.boolean(),
  requireCamera: z.boolean(),
  requireMicrophone: z.boolean(),
  startTime: z.date().optional(),
  endTime: z.date().optional(),
  status: z.nativeEnum(ExamStatus),
});

export const examSchema = examBaseSchema
  .refine((data) => {
    // Validate that passing marks don't exceed total marks
    if (data.passingMarks && data.passingMarks > data.totalMarks) {
      return false;
    }
    return true;
  }, {
    message: "Passing marks cannot exceed total marks",
    path: ["passingMarks"],
  })
  .refine((data) => {
    // Validate that end time is after start time
    if (data.startTime && data.endTime && data.endTime <= data.startTime) {
      return false;
    }
    return true;
  }, {
    message: "End time must be after start time",
    path: ["endTime"],
  });

export const examQuestionSchema = z.object({
  questionId: z.string().min(1, 'Question ID is required'),
  order: z.number().int().min(0),
  marks: z.number().int().min(1, 'Marks must be at least 1').max(100, 'Marks cannot exceed 100'),
});

export const createExamSchema = examBaseSchema.extend({
  questions: z.array(examQuestionSchema).min(1, 'Exam must have at least one question'),
}).refine((data) => {
  // Validate that passing marks don't exceed total marks
  if (data.passingMarks && data.passingMarks > data.totalMarks) {
    return false;
  }
  return true;
}, {
  message: "Passing marks cannot exceed total marks",
  path: ["passingMarks"],
}).refine((data) => {
  // Validate that end time is after start time
  if (data.startTime && data.endTime && data.endTime <= data.startTime) {
    return false;
  }
  return true;
}, {
  message: "End time must be after start time",
  path: ["endTime"],
});

// Student answer schemas
export const studentAnswerSchema = z.object({
  questionId: z.string().min(1, 'Question ID is required'),
  textAnswer: z.string().max(5000, 'Answer is too long').optional(),
  selectedOptions: z.array(z.string()).optional(),
  fileUrl: z.string().url('Please enter a valid URL').optional(),
  flagged: z.boolean().optional(),
});

export const submitExamSchema = z.object({
  answers: z.array(studentAnswerSchema),
  timeSpent: z.number().int().min(0).optional(),
});

// Session and proctoring schemas
export const sessionFlagSchema = z.object({
  type: z.string().min(1, 'Flag type is required'),
  description: z.string().min(1, 'Description is required').max(500, 'Description is too long'),
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  screenshotUrl: z.string().url('Please enter a valid URL').optional(),
  videoClipUrl: z.string().url('Please enter a valid URL').optional(),
});

export const resolveSessionFlagSchema = z.object({
  resolution: z.string().min(1, 'Resolution is required').max(1000, 'Resolution is too long'),
});

// Admin schemas
export const createUserSchema = z.object({
  email: emailSchema,
  firstName: nameSchema,
  lastName: nameSchema,
  role: z.nativeEnum(UserRole),
  phoneNumber: phoneSchema,
  isActive: z.boolean().optional(),
});

export const updateUserSchema = z.object({
  firstName: nameSchema.optional(),
  lastName: nameSchema.optional(),
  role: z.nativeEnum(UserRole).optional(),
  phoneNumber: phoneSchema,
  isActive: z.boolean().optional(),
});

// Search and filter schemas
export const searchSchema = z.object({
  query: z.string().max(100, 'Search query is too long').optional(),
  page: z.number().int().min(1).optional(),
  limit: z.number().int().min(1).max(100).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

export const examFilterSchema = searchSchema.extend({
  status: z.nativeEnum(ExamStatus).optional(),
  instructorId: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
});

export const userFilterSchema = searchSchema.extend({
  role: z.nativeEnum(UserRole).optional(),
  isActive: z.boolean().optional(),
});

// File upload schema
export const fileUploadSchema = z.object({
  file: z.any().refine((file) => {
    if (!file) return false;
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    return allowedTypes.includes(file.type);
  }, 'Invalid file type').refine((file) => {
    if (!file) return false;
    const maxSize = 10 * 1024 * 1024; // 10MB
    return file.size <= maxSize;
  }, 'File size must be less than 10MB'),
});

// Export type inference helpers
export type LoginInput = z.infer<typeof loginSchema>;
export type SignupInput = z.infer<typeof signupSchema>;
export type ForgotPasswordInput = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordInput = z.infer<typeof resetPasswordSchema>;
export type OTPInput = z.infer<typeof otpSchema>;
export type ProfileSetupInput = z.infer<typeof profileSetupSchema>;
export type UpdateProfileInput = z.infer<typeof updateProfileSchema>;
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>;
export type QuestionInput = z.infer<typeof questionSchema>;
export type ExamInput = z.infer<typeof examSchema>;
export type CreateExamInput = z.infer<typeof createExamSchema>;
export type StudentAnswerInput = z.infer<typeof studentAnswerSchema>;
export type SubmitExamInput = z.infer<typeof submitExamSchema>;
export type SessionFlagInput = z.infer<typeof sessionFlagSchema>;
export type CreateUserInput = z.infer<typeof createUserSchema>;
export type UpdateUserInput = z.infer<typeof updateUserSchema>;
export type SearchInput = z.infer<typeof searchSchema>;
export type ExamFilterInput = z.infer<typeof examFilterSchema>;
export type UserFilterInput = z.infer<typeof userFilterSchema>;
