'use client';

import React from 'react';
import { useFormContext, Controller } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

export interface FormFieldProps {
  name: string;
  label?: string;
  type?: string;
  placeholder?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  className?: string;
}

export const FormField: React.FC<FormFieldProps> = ({
  name,
  label,
  type = 'text',
  placeholder,
  helperText,
  required = false,
  disabled = false,
  leftIcon,
  rightIcon,
  className,
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const error = errors[name]?.message as string;

  return (
    <div className={cn('w-full', className)}>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <Input
            {...field}
            type={type}
            label={label}
            placeholder={placeholder}
            helperText={helperText}
            error={error}
            required={required}
            disabled={disabled}
            leftIcon={leftIcon}
            rightIcon={rightIcon}
          />
        )}
      />
    </div>
  );
};

export interface TextAreaFieldProps {
  name: string;
  label?: string;
  placeholder?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  rows?: number;
  className?: string;
}

export const TextAreaField: React.FC<TextAreaFieldProps> = ({
  name,
  label,
  placeholder,
  helperText,
  required = false,
  disabled = false,
  rows = 4,
  className,
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const error = errors[name]?.message as string;
  const fieldId = `textarea-${name}`;

  return (
    <div className={cn('w-full', className)}>
      {label && (
        <label 
          htmlFor={fieldId}
          className="block text-sm font-medium text-secondary-700 mb-1"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <textarea
            {...field}
            id={fieldId}
            rows={rows}
            placeholder={placeholder}
            disabled={disabled}
            className={cn(
              'w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-0 disabled:opacity-50 disabled:cursor-not-allowed resize-vertical',
              error
                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                : 'border-secondary-300 focus:border-primary-500 focus:ring-primary-500'
            )}
          />
        )}
      />
      
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
      
      {helperText && !error && (
        <p className="mt-1 text-sm text-secondary-500">{helperText}</p>
      )}
    </div>
  );
};

export interface SelectFieldProps {
  name: string;
  label?: string;
  placeholder?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  options: Array<{ value: string; label: string }>;
  className?: string;
}

export const SelectField: React.FC<SelectFieldProps> = ({
  name,
  label,
  placeholder = 'Select an option',
  helperText,
  required = false,
  disabled = false,
  options,
  className,
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const error = errors[name]?.message as string;
  const fieldId = `select-${name}`;

  return (
    <div className={cn('w-full', className)}>
      {label && (
        <label 
          htmlFor={fieldId}
          className="block text-sm font-medium text-secondary-700 mb-1"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <select
            {...field}
            id={fieldId}
            disabled={disabled}
            className={cn(
              'w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-0 disabled:opacity-50 disabled:cursor-not-allowed',
              error
                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                : 'border-secondary-300 focus:border-primary-500 focus:ring-primary-500'
            )}
          >
            <option value="">{placeholder}</option>
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )}
      />
      
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
      
      {helperText && !error && (
        <p className="mt-1 text-sm text-secondary-500">{helperText}</p>
      )}
    </div>
  );
};

export interface CheckboxFieldProps {
  name: string;
  label: string;
  helperText?: string;
  disabled?: boolean;
  className?: string;
}

export const CheckboxField: React.FC<CheckboxFieldProps> = ({
  name,
  label,
  helperText,
  disabled = false,
  className,
}) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const error = errors[name]?.message as string;
  const fieldId = `checkbox-${name}`;

  return (
    <div className={cn('w-full', className)}>
      <Controller
        name={name}
        control={control}
        render={({ field: { value, onChange } }) => (
          <div className="flex items-start">
            <div className="flex items-center h-5">
              <input
                id={fieldId}
                type="checkbox"
                checked={value || false}
                onChange={(e) => onChange(e.target.checked)}
                disabled={disabled}
                className={cn(
                  'h-4 w-4 rounded border-secondary-300 text-primary-600 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed',
                  error && 'border-red-300'
                )}
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor={fieldId} className="font-medium text-secondary-700">
                {label}
              </label>
              {helperText && (
                <p className="text-secondary-500">{helperText}</p>
              )}
            </div>
          </div>
        )}
      />
      
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};
