'use client';

import React from 'react';
import Link from 'next/link';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { AuthLayout } from '@/components/layouts';
import { FormField, CheckboxField } from '@/components/forms';
import { Button } from '@/components/ui';
import { signupSchema } from '@/lib/validations';
import { z } from 'zod';
import { UserRole } from '@/types';
import { useAuth } from '@/lib/auth/context';
import { useRouter } from 'next/navigation';

type SignupFormData = {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  adminCode: string;
  agreeToTerms: boolean;
};

export default function AdminSignupPage() {
  const router = useRouter();
  const { signup } = useAuth();
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const methods = useForm<SignupFormData>({
    resolver: zodResolver(signupSchema.extend({
      adminCode: z.string().min(1, 'Admin authorization code is required'),
      agreeToTerms: z.boolean().refine(val => val === true, {
        message: 'You must agree to the terms and conditions',
      }),
    })),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      firstName: '',
      lastName: '',
      phoneNumber: '',
      adminCode: '',
      agreeToTerms: false,
    },
  });

  const onSubmit = async (data: SignupFormData) => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await signup({
        ...data,
        role: UserRole.ADMIN,
      });

      if (result.success) {
        router.push('/login?message=Admin account created successfully. Please check your email to verify your account.');
      } else {
        setError(result.error || 'Failed to create account');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthLayout
      title="Administrator Registration"
      subtitle="Create your admin account to manage the system"
    >
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            <p className="text-sm">
              <strong>Restricted Access:</strong> Administrator accounts require a special authorization code. 
              Only authorized university personnel should create admin accounts.
            </p>
          </div>

          <FormField
            name="adminCode"
            label="Admin Authorization Code"
            placeholder="Enter the admin authorization code"
            required
            helperText="Contact your IT department for the authorization code"
          />

          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <FormField
              name="firstName"
              label="First Name"
              placeholder="Enter your first name"
              required
            />
            <FormField
              name="lastName"
              label="Last Name"
              placeholder="Enter your last name"
              required
            />
          </div>

          <FormField
            name="email"
            label="Email Address"
            type="email"
            placeholder="Enter your university email"
            required
            helperText="Use your official university email address"
          />

          <FormField
            name="phoneNumber"
            label="Phone Number"
            type="tel"
            placeholder="Enter your phone number"
            required
            helperText="Required for admin verification and security"
          />

          <FormField
            name="password"
            label="Password"
            type="password"
            placeholder="Create a strong password"
            required
            helperText="Must be at least 8 characters with uppercase, lowercase, and number"
          />

          <FormField
            name="confirmPassword"
            label="Confirm Password"
            type="password"
            placeholder="Confirm your password"
            required
          />

          <CheckboxField
            name="agreeToTerms"
            label={
              <>
                I agree to the{' '}
                <Link href="/terms" className="text-primary-600 hover:text-primary-500">
                  Terms of Service
                </Link>{' '}
                and{' '}
                <Link href="/privacy" className="text-primary-600 hover:text-primary-500">
                  Privacy Policy
                </Link>
              </>
            }
          />

          <Button
            type="submit"
            className="w-full"
            isLoading={isLoading}
            disabled={isLoading}
          >
            Create Administrator Account
          </Button>

          <div className="text-center">
            <p className="text-sm text-secondary-600">
              Already have an account?{' '}
              <Link href="/login" className="text-primary-600 hover:text-primary-500 font-medium">
                Sign in here
              </Link>
            </p>
            <p className="text-sm text-secondary-600 mt-2">
              Are you a student?{' '}
              <Link href="/signup/student" className="text-primary-600 hover:text-primary-500 font-medium">
                Register as student
              </Link>
            </p>
          </div>
        </form>
      </FormProvider>
    </AuthLayout>
  );
}
