'use client';

import React from 'react';
import Link from 'next/link';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter, useSearchParams } from 'next/navigation';
import { AuthLayout } from '@/components/layouts';
import { FormField, CheckboxField } from '@/components/forms';
import { Button } from '@/components/ui';
import { loginSchema } from '@/lib/validations';
import { useAuth } from '@/lib/auth/context';
import { UserRole } from '@/types';

type LoginFormData = {
  email: string;
  password: string;
  rememberMe: boolean;
};

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login } = useAuth();
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  // Get success message from URL params (e.g., after signup)
  const message = searchParams.get('message');

  const methods = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await login(data.email, data.password);

      if (result.success) {
        // Redirect based on user role
        const redirectPath = getRedirectPath();
        router.push(redirectPath);
      } else {
        setError(result.error || 'Login failed');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const getRedirectPath = () => {
    // Check if there's a redirect URL in the search params
    const redirect = searchParams.get('redirect');
    if (redirect) {
      return decodeURIComponent(redirect);
    }

    // Default redirects based on role (will be determined after login)
    return '/dashboard';
  };

  return (
    <AuthLayout
      title="Sign In"
      subtitle="Welcome back to the University Exam Portal"
    >
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-6">
          {message && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
              {message}
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          <FormField
            name="email"
            label="Email Address"
            type="email"
            placeholder="Enter your email"
            required
          />

          <FormField
            name="password"
            label="Password"
            type="password"
            placeholder="Enter your password"
            required
          />

          <div className="flex items-center justify-between">
            <CheckboxField
              name="rememberMe"
              label="Remember me"
            />
            <Link 
              href="/forgot-password" 
              className="text-sm text-primary-600 hover:text-primary-500"
            >
              Forgot password?
            </Link>
          </div>

          <Button
            type="submit"
            className="w-full"
            isLoading={isLoading}
            disabled={isLoading}
          >
            Sign In
          </Button>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-secondary-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-secondary-500">Don't have an account?</span>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-1 gap-3">
              <Link
                href="/signup/student"
                className="w-full inline-flex justify-center py-2 px-4 border border-secondary-300 rounded-md shadow-sm bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50 transition-colors"
              >
                Register as Student
              </Link>
              
              <div className="grid grid-cols-2 gap-3">
                <Link
                  href="/signup/instructor"
                  className="inline-flex justify-center py-2 px-4 border border-secondary-300 rounded-md shadow-sm bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50 transition-colors"
                >
                  Instructor
                </Link>
                <Link
                  href="/signup/proctor"
                  className="inline-flex justify-center py-2 px-4 border border-secondary-300 rounded-md shadow-sm bg-white text-sm font-medium text-secondary-500 hover:bg-secondary-50 transition-colors"
                >
                  Proctor
                </Link>
              </div>
            </div>
          </div>
        </form>
      </FormProvider>
    </AuthLayout>
  );
}
