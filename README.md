# University Exam Portal

A comprehensive online examination platform built with Next.js, featuring secure exam delivery, real-time proctoring, and role-based access control for students, instructors, proctors, and administrators.

## 🚀 Features

- **Secure Authentication** - Role-based signup and login with email verification
- **Multi-Role Support** - Students, Instructors, Proctors, and Administrators
- **Exam Management** - Create, edit, and manage exams with various question types
- **Real-time Proctoring** - Live monitoring with AI-powered behavior detection
- **Responsive Design** - Green and white university theme with Tailwind CSS
- **Type Safety** - Full TypeScript implementation with Prisma ORM
- **Modern Stack** - Next.js 15, React 19, PostgreSQL, and more

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, Type<PERSON>, Tailwind CSS v4
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL
- **Authentication**: NextAuth.js with custom providers
- **UI Components**: Headless UI, Heroicons
- **Forms**: React Hook Form with Zod validation
- **State Management**: <PERSON>ustand, React Query

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v18 or higher)
- **PostgreSQL** (v12 or higher)
- **npm** or **yarn** package manager

## 🚀 Getting Started

### 1. Clone the Repository

```bash
git clone <repository-url>
cd online-exam-app
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Set Up Environment Variables

Copy the example environment file and update it with your configuration:

```bash
cp .env.example .env.local
```

Update `.env.local` with your database and other configuration:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/online_exam_db?schema=public"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# JWT
JWT_SECRET="your-jwt-secret-here"

# Application
APP_NAME="University Exam Portal"
APP_URL="http://localhost:3000"

# Email Configuration (optional for development)
EMAIL_FROM="<EMAIL>"
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
```

### 4. Set Up the Database

Make sure PostgreSQL is running, then set up the database:

```bash
# Create the database (if it doesn't exist)
createdb online_exam_db

# Set up the database schema
npm run db:setup
```

This will:
- Generate the Prisma client
- Push the database schema
- Set up all required tables

### 5. Start the Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
online-exam-app/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication routes
│   │   ├── login/         # Unified login page
│   │   └── signup/        # Role-specific signup pages
│   ├── student/           # Student dashboard and features
│   ├── instructor/        # Instructor dashboard and features
│   ├── proctor/           # Proctor dashboard and features
│   ├── admin/             # Admin dashboard and features
│   └── globals.css        # Global styles with green theme
├── components/            # Reusable components
│   ├── ui/               # Basic UI components
│   ├── forms/            # Form components
│   └── layouts/          # Layout components
├── lib/                  # Utility libraries
│   ├── auth/             # Authentication logic
│   ├── db/               # Database configuration
│   ├── utils/            # Utility functions
│   └── validations/      # Zod schemas
├── prisma/               # Database schema
├── types/                # TypeScript type definitions
└── hooks/                # Custom React hooks
```

## 🎨 Design System

The application uses a green and white color scheme representing university branding:

- **Primary Green**: `#22c55e` (green-500)
- **Secondary Gray**: Various shades for text and backgrounds
- **Success/Warning/Error**: Complementary colors for status indicators

## 👥 User Roles

### Students
- Register with university email
- Take exams with real-time monitoring
- View results and feedback
- Access: `/signup/student`, `/student/*`

### Instructors
- Create and manage exams
- Build question banks
- View analytics and reports
- Requires approval after registration
- Access: `/signup/instructor`, `/instructor/*`

### Proctors
- Monitor live exam sessions
- Flag suspicious behavior
- Review and resolve incidents
- Requires special authorization
- Access: `/signup/proctor`, `/proctor/*`

### Administrators
- Manage all users and system settings
- Configure platform settings
- View audit logs and reports
- Requires admin authorization code
- Access: `/signup/admin`, `/admin/*`

## 🔧 Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Database
npm run db:setup     # Set up database (generate + push)
npm run db:push      # Push schema changes
npm run db:generate  # Generate Prisma client
npm run db:studio    # Open Prisma Studio
```

## 🔒 Security Features

- **Role-based Access Control** - Middleware protection for all routes
- **Input Validation** - Zod schemas for all forms and API endpoints
- **SQL Injection Protection** - Prisma ORM with parameterized queries
- **Authentication** - Secure JWT-based authentication
- **Password Security** - bcrypt hashing with salt rounds

## 🚧 Development Status

This project is currently in **Phase 1: Foundation & Setup** of the development plan:

- ✅ Project dependencies and setup
- ✅ Database schema and Prisma configuration
- ✅ Green/white design system with Tailwind CSS
- ✅ TypeScript interfaces and types
- ✅ Global state management
- ✅ Shared component library
- ✅ Role-based authentication pages
- ✅ Basic layout system

**Next Steps**: Phase 2 - Authentication & Authorization implementation

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

For support and questions, please contact the development team or create an issue in the repository.
